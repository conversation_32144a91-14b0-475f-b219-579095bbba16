import React, { useState, useRef, useEffect } from 'react'
import { authAPI } from '../utils/supabase.mjs'
import { useToast } from '../contexts/ToastContext.jsx'
import { AuthThemeToggle } from '../components/ui/AuthThemeToggle.jsx'

/**
 * Sign Up (Registration) Screen Component with Unified Glass Design
 * Matches WelcomeGlass design structure and theme system
 * 
 * @param {Object} props
 * @param {Function} props.onAuthenticated - Callback for when registration is completed
 * @param {Function} props.onNavigateToLogin - Callback to navigate to login
 * @param {Function} props.onNavigateToForgotPassword - Callback to navigate to forgot password
 * @param {boolean} props.isLightTheme - Current theme state
 * @param {Function} props.onToggleTheme - Theme toggle handler
 */
const SignUp = ({ onAuthenticated, onNavigateToLogin, onNavigateToForgotPassword, isLightTheme, onToggleTheme }) => {
    // CONTEXT7MCP: Toast integration for account creation success
    const { showSuccess } = useToast();
    
    // State for form inputs
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        confirmPassword: '',
        fullName: ''
    });
    
    // State for validation errors
    const [errors, setErrors] = useState({
        email: '',
        password: '',
        confirmPassword: '',
        fullName: '',
        auth: ''
    });

    // Touched fields tracking for validation
    const [touched, setTouched] = useState({
        email: false,
        password: false,
        confirmPassword: false,
        fullName: false
    });
    
    // Refs for form fields
    const inputRefs = {
        email: useRef(null),
        password: useRef(null),
        confirmPassword: useRef(null),
        fullName: useRef(null)
    };
    
    // State for password visibility, loading, and success
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [registrationSuccess, setRegistrationSuccess] = useState(false);
    const [needsEmailConfirmation, setNeedsEmailConfirmation] = useState(false);
    
    // DEBUG: Skip confirmation for development testing
    const [showDebugControls] = useState(process.env.NODE_ENV === 'development');

    // CONTEXT7MCP: Show success toast when account creation is successful
    useEffect(() => {
        if (registrationSuccess) {
            // Prevent duplicate toasts with session storage flag
            const toastShown = sessionStorage.getItem('signup_success_toast_shown');
            if (!toastShown) {
                // Show success toast with personalized message
                const userName = formData.fullName ? formData.fullName.split(' ')[0] : 'there';
                showSuccess(`Welcome to Thumbspark, ${userName}! Your account has been created successfully.`, 4000);
                
                // Set flag to prevent duplicate toasts
                sessionStorage.setItem('signup_success_toast_shown', 'true');
                
                // Clear flag after a reasonable time (5 minutes)
                setTimeout(() => {
                    sessionStorage.removeItem('signup_success_toast_shown');
                }, 300000);
            }
        }
    }, [registrationSuccess, formData.fullName, showSuccess]);

    // Validation functions
    const validateEmail = (emailInput) => {
        const email = emailInput ? emailInput.trim() : '';
        if (!email) return "Email is required";
        const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i;
        if (!emailRegex.test(email)) return "Invalid email address";
        return "";
    };
    
    const validatePassword = (password) => {
        if (!password) return "Password is required";
        if (password.length < 6) return "Password must be at least 6 characters long";
        return "";
    };

    const validateConfirmPassword = (confirmPassword, password) => {
        if (!confirmPassword) return "Please confirm your password";
        if (confirmPassword !== password) return "Passwords do not match";
        return "";
    };

    const validateFullName = (fullName) => {
        if (!fullName) return "Full name is required";
        if (fullName.trim().length < 2) return "Full name must be at least 2 characters";
        return "";
    };
    
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        
        setFormData(prev => ({ ...prev, [name]: value }));
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        else if (name === 'password') errorMessage = validatePassword(value);
        else if (name === 'confirmPassword') errorMessage = validateConfirmPassword(value, formData.password);
        else if (name === 'fullName') errorMessage = validateFullName(value);
        
        setErrors(prev => ({ ...prev, [name]: errorMessage, auth: '' }));

        // Re-validate confirm password if password changes
        if (name === 'password' && touched.confirmPassword) {
            const confirmPasswordError = validateConfirmPassword(formData.confirmPassword, value);
            setErrors(prev => ({ ...prev, confirmPassword: confirmPasswordError }));
        }
    };
    
    const handleBlur = (e) => {
        const { name, value } = e.target;
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        else if (name === 'password') errorMessage = validatePassword(value);
        else if (name === 'confirmPassword') errorMessage = validateConfirmPassword(value, formData.password);
        else if (name === 'fullName') errorMessage = validateFullName(value);
        
        setErrors(prev => ({ ...prev, [name]: errorMessage }));
    };
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        const fieldsToTouch = { email: true, password: true, confirmPassword: true, fullName: true };
        setTouched(prev => ({ ...prev, ...fieldsToTouch }));

        const currentErrors = {
            email: validateEmail(formData.email),
            password: validatePassword(formData.password),
            confirmPassword: validateConfirmPassword(formData.confirmPassword, formData.password),
            fullName: validateFullName(formData.fullName)
        };
        setErrors(prev => ({ ...prev, ...currentErrors, auth: '' }));

        if (Object.values(currentErrors).some(error => error)) {
            const firstErrorField = Object.keys(currentErrors).find(key => currentErrors[key]);
            if (firstErrorField && inputRefs[firstErrorField]?.current) {
                inputRefs[firstErrorField].current.focus();
            }
            return;
        }
        
        setIsSubmitting(true);
        
        try {
            // Register with Supabase
            const result = await authAPI.signUp(formData.email, formData.password, formData.fullName);
            
            if (result.success) {
                setRegistrationSuccess(true);
                
                if (result.needsConfirmation) {
                    // Email confirmation required
                    setNeedsEmailConfirmation(true);
                } else {
                    // Registration complete, can authenticate immediately
                    if (result.data?.user) {
                        setTimeout(() => {
                            onAuthenticated(result.data.user);
                        }, 2000);
                    }
                }
            } else {
                // Registration failed
                setErrors(prev => ({ 
                    ...prev, 
                    auth: result.error || 'Registration failed. Please try again.' 
                }));
            }
        } catch (error) {
            console.error('Registration error:', error);
            setErrors(prev => ({ 
                ...prev, 
                auth: 'An unexpected error occurred. Please try again.' 
            }));
        } finally {
            setIsSubmitting(false);
        }
    };
    
    const handleGoogleSignIn = async () => {
        setIsSubmitting(true);
        setErrors(prev => ({ ...prev, auth: '' }));
        
        try {
            const result = await authAPI.signInWithGoogle();
            
            if (result.success && result.data?.user) {
                // Google sign-in successful
                onAuthenticated(result.data.user);
            } else {
                // Google sign-in failed
                setErrors(prev => ({
                    ...prev,
                    auth: result.error || 'Failed to sign in with Google. Please try again.'
                }));
                setIsSubmitting(false);
            }
        } catch (error) {
            console.error('Google sign-in error:', error);
            setErrors(prev => ({
                ...prev,
                auth: 'An unexpected error occurred with Google sign-in. Please try again.'
            }));
            setIsSubmitting(false);
        }
    };

    // If registration was successful and needs email confirmation, show confirmation screen
    if (registrationSuccess && needsEmailConfirmation) {
        return (
            <div className="auth-glass-container" id="signup-glass-container">
                {/* Theme Toggle Button */}
                <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
                
                {/* Centered Glass Card */}
                <div className="flex items-center justify-center min-h-screen p-4">
                    <div className="auth-glass-card w-full max-w-md p-8" id="signup-confirmation-card">
                        {/* Logo */}
                        <img 
                            src="/assets/main-logo.svg" 
                            alt="Thumbspark Logo" 
                            className="auth-glass-logo" 
                            draggable="false" 
                        />

                        {/* Success Icon */}
                        <div className="mb-6 flex justify-center">
                            <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center">
                                <span className="iconify text-green-400" data-icon="solar:letter-bold" style={{ fontSize: '40px' }}></span>
                            </div>
                        </div>

                        {/* Title and Message */}
                        <h1 className="auth-glass-title">Check Your Email</h1>
                        <p className="auth-glass-subtitle">We've sent you a confirmation link</p>

                        <div className="mb-8">
                            <p className="text-gray-400 mb-4 text-center">
                                We've sent a confirmation link to:
                            </p>
                            <p className="text-white font-medium mb-6 text-center">
                                {formData.email}
                            </p>
                            <div className="bg-blue-900/20 border border-blue-700/50 rounded-lg p-4 mb-6">
                                <p className="text-blue-300 text-sm mb-2">
                                    <strong>Next steps:</strong>
                                </p>
                                <ol className="text-gray-400 text-sm text-left list-decimal list-inside space-y-1">
                                    <li>Check your email inbox (and spam folder)</li>
                                    <li>Click the "Confirm Email" link in the email</li>
                                    <li>You'll be automatically signed in</li>
                                </ol>
                            </div>
                        </div>

                        {/* Navigation Links */}
                        <div className="text-center space-y-4">
                            <button 
                                type="button"
                                onClick={onNavigateToLogin}
                                className="auth-glass-cta-btn w-full"
                            >
                                Back to Sign In
                            </button>
                            
                            {showDebugControls && (
                                <button 
                                    type="button"
                                    onClick={() => {
                                        console.log('DEBUG: Skipping email confirmation');
                                        onAuthenticated({ email: formData.email, user_metadata: { full_name: formData.fullName } });
                                    }}
                                    className="auth-glass-link"
                                >
                                    [DEBUG] Skip Email Confirmation
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="auth-glass-container" id="signup-glass-container">
            {/* Theme Toggle Button */}
            <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
            
            {/* Centered Glass Card */}
            <div className="flex items-center justify-center min-h-screen p-4">
                <div className="auth-glass-card w-full max-w-md p-8" id="signup-glass-card">
                    {/* Logo */}
                    <img 
                        src="/assets/main-logo.svg" 
                        alt="Thumbspark Logo" 
                        className="auth-glass-logo" 
                        draggable="false" 
                    />

                    {/* Title and Subtitle */}
                    <h1 className="auth-glass-title">Create Account</h1>
                    <p className="auth-glass-subtitle">Join Thumbspark today</p>

                    {/* Global Error Message */}
                    {errors.auth && (
                        <div className="auth-glass-global-error" id="signup-glass-global-error">
                            <div className="auth-glass-global-error-text">
                                <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                {errors.auth}
                            </div>
                        </div>
                    )}

                    {/* Registration Form */}
                    <form onSubmit={handleSubmit} className="space-y-4" id="signup-glass-form">
                        {/* Google Sign In Button - Dark Theme */}
                        <button
                            type="button"
                            onClick={handleGoogleSignIn}
                            disabled={isSubmitting}
                            className="auth-glass-google-btn-dark"
                            id="signup-glass-google-btn"
                        >
                            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
                                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="currentColor"/>
                                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="currentColor"/>
                                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="currentColor"/>
                                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="currentColor"/>
                            </svg>
                            Continue with Google
                        </button>
                        
                        {/* Divider */}
                        <div className="auth-glass-divider">
                            <span className="auth-glass-divider-text">or</span>
                        </div>

                        {/* Full Name Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="fullName" className="auth-glass-label">
                                Full Name
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:user-linear"></span>
                                <input 
                                    type="text" 
                                    id="signup-glass-fullname-input" 
                                    name="fullName" 
                                    value={formData.fullName} 
                                    onChange={handleInputChange} 
                                    onBlur={handleBlur} 
                                    ref={inputRefs.fullName} 
                                    required
                                    className={`auth-glass-input ${errors.fullName && touched.fullName ? 'error' : ''}`}
                                    placeholder="Enter your full name"
                                />
                            </div>
                            {errors.fullName && touched.fullName && (
                                <div className="auth-glass-error-message">
                                    <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                    {errors.fullName}
                                </div>
                            )}
                        </div>

                        {/* Email Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="email" className="auth-glass-label">
                                Email Address
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:letter-linear"></span>
                                <input 
                                    type="email" 
                                    id="signup-glass-email-input" 
                                    name="email" 
                                    value={formData.email} 
                                    onChange={handleInputChange} 
                                    onBlur={handleBlur} 
                                    ref={inputRefs.email} 
                                    required
                                    className={`auth-glass-input ${errors.email && touched.email ? 'error' : ''}`}
                                    placeholder="Enter your email address"
                                />
                            </div>
                            {errors.email && touched.email && (
                                <div className="auth-glass-error-message">
                                    <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                    {errors.email}
                                </div>
                            )}
                        </div>

                        {/* Password Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="password" className="auth-glass-label">
                                Password
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:lock-keyhole-linear"></span>
                                <input 
                                    type={showPassword ? "text" : "password"} 
                                    id="signup-glass-password-input" 
                                    name="password" 
                                    value={formData.password} 
                                    onChange={handleInputChange} 
                                    onBlur={handleBlur} 
                                    ref={inputRefs.password} 
                                    required
                                    className={`auth-glass-input ${errors.password && touched.password ? 'error' : ''}`}
                                    placeholder="Enter your password"
                                />
                                <button 
                                    type="button" 
                                    onClick={() => setShowPassword(!showPassword)} 
                                    className="auth-glass-password-toggle"
                                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                                >
                                    <span className="iconify" data-icon={showPassword ? "solar:eye-bold" : "solar:eye-closed-bold"}></span>
                                </button>
                            </div>
                            {errors.password && touched.password && (
                                <div className="auth-glass-error-message">
                                    <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                    {errors.password}
                                </div>
                            )}
                        </div>

                        {/* Confirm Password Field */}
                        <div className="auth-glass-input-group">
                            <label htmlFor="confirmPassword" className="auth-glass-label">
                                Confirm Password
                            </label>
                            <div className="auth-glass-input-wrapper">
                                <span className="auth-glass-input-icon iconify" data-icon="solar:lock-keyhole-linear"></span>
                                <input 
                                    type={showConfirmPassword ? "text" : "password"} 
                                    id="signup-glass-confirmpassword-input" 
                                    name="confirmPassword" 
                                    value={formData.confirmPassword} 
                                    onChange={handleInputChange} 
                                    onBlur={handleBlur} 
                                    ref={inputRefs.confirmPassword} 
                                    required
                                    className={`auth-glass-input ${errors.confirmPassword && touched.confirmPassword ? 'error' : ''}`}
                                    placeholder="Confirm your password"
                                />
                                <button 
                                    type="button" 
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)} 
                                    className="auth-glass-password-toggle"
                                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                                >
                                    <span className="iconify" data-icon={showConfirmPassword ? "solar:eye-bold" : "solar:eye-closed-bold"}></span>
                                </button>
                            </div>
                            {errors.confirmPassword && touched.confirmPassword && (
                                <div className="auth-glass-error-message">
                                    <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                    {errors.confirmPassword}
                                </div>
                            )}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            disabled={isSubmitting || Object.values(errors).some(error => error && error !== errors.auth)}
                            className="auth-glass-cta-btn"
                            id="signup-glass-submit-btn"
                        >
                            {isSubmitting ? (
                                <>
                                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Creating Account...</span>
                                </>
                            ) : (
                                'Create Account'
                            )}
                        </button>

                        {/* Additional Links */}
                        <div className="text-center">
                            <span className="text-gray-400 text-sm">
                                Already have an account?{' '}
                                <button 
                                    type="button"
                                    onClick={onNavigateToLogin}
                                    className="auth-glass-link"
                                >
                                    Sign in here
                                </button>
                            </span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default SignUp; 